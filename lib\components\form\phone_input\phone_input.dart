import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octasync_client/components/form/phone_input/phone_input_enum.dart';
import 'package:octasync_client/imports.dart';

class PhoneInput extends StatefulWidget {
  final void Function(String? areaCode, String? phoneNumber)? onChange;

  const PhoneInput({super.key, this.onChange});

  @override
  State<PhoneInput> createState() => _PhoneInputState();
}

class _PhoneInputState extends State<PhoneInput> {
  final SelectController<String> _areaCodeController = SelectController<String>();
  final TextEditingController _phoneController = TextEditingController();

  void change() {
    widget.onChange?.call(_areaCodeController.value, _phoneController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 100,
          child: AppSelect<String>(
            placeholder: '区号',
            options: PhonmeInputEnum.areaCodeOptions,
            controller: _areaCodeController,
            onChanged: (value) => change(),
          ),
        ),
        SizedBox(width: 10),
        Expanded(
          child: AppInput(
            label: "",
            hintText: "手机号",
            size: InputSize.medium,
            controller: _phoneController,
            maxLength: 30,
            inputFormatters: [FilteringTextInputFormatter(RegExp(r'[0-9]'), allow: true)],
            onChanged: (value) => change(),
          ),
        ),
      ],
    );
  }
}
